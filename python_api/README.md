# ChatGPT API Wrapper

A simple, barebones Python API for interacting with OpenAI's ChatGPT API. This wrapper provides an easy-to-use interface where you can provide a prompt as input and get the response as output.

## Features

- Simple prompt-to-response interface
- **Token usage control** for cost management
- Support for system messages
- Streaming responses
- Environment variable configuration
- Error handling
- Quick utility functions

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your OpenAI API key:
```bash
# Option 1: Set environment variable
export OPENAI_API_KEY="your_api_key_here"

# Option 2: Create a .env file
cp .env.example .env
# Edit .env and add your API key
```

## Usage

### Basic Usage

```python
from chatgpt_api import ChatGPTAPI

# Initialize the API with default 1000 token limit
api = ChatGPTAPI()

# Send a prompt and get response
response = api.chat("What is Python programming?")
print(response)
```

### Token Control for Cost Management

```python
from chatgpt_api import Chat<PERSON><PERSON><PERSON>

# Initialize with custom token limit
api = ChatGPTAPI(max_tokens=500)  # Limit responses to 500 tokens

# Or override per request
response = api.chat("Explain machine learning", max_tokens=100)
print(response)
```

### Quick Function

```python
from chatgpt_api import quick_chat

# One-liner for simple prompts
response = quick_chat("Tell me a joke about programming")
print(response)

# With token limit
response = quick_chat("Explain AI", max_tokens=200)
print(response)
```

### With System Message

```python
from chatgpt_api import ChatGPTAPI

api = ChatGPTAPI()

# Set the behavior of the assistant
system_message = "You are a helpful coding tutor."
response = api.chat(
    "How do I create a function in Python?",
    system_message=system_message
)
print(response)
```

### Streaming Response

```python
from chatgpt_api import ChatGPTAPI

api = ChatGPTAPI()

# Get response as it's generated
for chunk in api.chat_stream("Write a short story about AI", max_tokens=300):
    print(chunk, end="", flush=True)
```

### Custom Model

```python
from chatgpt_api import ChatGPTAPI

# Use a different model
api = ChatGPTAPI(model="gpt-3.5-turbo")
response = api.chat("Hello, world!")
print(response)
```

## Configuration

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `OPENAI_MODEL`: Default model to use (optional, defaults to "gpt-4o")
- `OPENAI_MAX_TOKENS`: Default maximum tokens per response (optional, defaults to 1000)

### API Key Options

1. **Environment variable**: Set `OPENAI_API_KEY`
2. **Constructor parameter**: `ChatGPTAPI(api_key="your_key")`
3. **`.env` file**: Create a `.env` file with your API key

## Example

Run the example script to see the API in action:

```bash
python example.py
```

## Error Handling

The API includes basic error handling. If there's an issue with the API call, it will raise an exception with a descriptive message.

```python
try:
    response = api.chat("Hello!")
    print(response)
except Exception as e:
    print(f"Error: {e}")
```

## Requirements

- Python 3.7+
- OpenAI API key
- Internet connection

## License

This project follows the same license as the parent repository.

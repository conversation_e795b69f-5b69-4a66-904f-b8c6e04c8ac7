"""
Simple ChatGPT API wrapper for easy interaction with OpenAI's API.

This module provides a barebones interface to ChatGPT that takes a prompt
as input and returns the response as output.
"""

import os
from typing import Optional
from openai import OpenAI
from dotenv import load_dotenv


class ChatGPTAPI:
    """
    A simple wrapper for OpenAI's ChatGPT API.
    
    This class provides a straightforward interface to interact with ChatGPT
    by sending prompts and receiving responses.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4o"):
        """
        Initialize the ChatGPT API client.
        
        Args:
            api_key (str, optional): OpenAI API key. If not provided, will try to
                                   load from environment variable OPENAI_API_KEY.
            model (str): The model to use for chat completions. Defaults to "gpt-4o".
        
        Raises:
            ValueError: If no API key is provided or found in environment.
        """
        # Load environment variables from .env file if it exists
        load_dotenv()
        
        # Use provided API key or get from environment
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError(
                "OpenAI API key is required. Provide it as a parameter or set "
                "the OPENAI_API_KEY environment variable."
            )
        
        # Set model (allow override from environment)
        self.model = os.getenv("OPENAI_MODEL", model)
        
        # Initialize OpenAI client
        self.client = OpenAI(api_key=self.api_key)
    
    def chat(self, prompt: str, system_message: Optional[str] = None) -> str:
        """
        Send a prompt to ChatGPT and get the response.
        
        Args:
            prompt (str): The user's prompt/question to send to ChatGPT.
            system_message (str, optional): System message to set the behavior
                                          of the assistant.
        
        Returns:
            str: The response from ChatGPT.
        
        Raises:
            Exception: If there's an error with the API call.
        """
        try:
            # Prepare messages
            messages = []
            
            # Add system message if provided
            if system_message:
                messages.append({"role": "system", "content": system_message})
            
            # Add user prompt
            messages.append({"role": "user", "content": prompt})
            
            # Make API call
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages
            )
            
            # Extract and return the response content
            return response.choices[0].message.content
            
        except Exception as e:
            raise Exception(f"Error calling ChatGPT API: {str(e)}")
    
    def chat_stream(self, prompt: str, system_message: Optional[str] = None):
        """
        Send a prompt to ChatGPT and get a streaming response.
        
        Args:
            prompt (str): The user's prompt/question to send to ChatGPT.
            system_message (str, optional): System message to set the behavior
                                          of the assistant.
        
        Yields:
            str: Chunks of the response from ChatGPT as they arrive.
        
        Raises:
            Exception: If there's an error with the API call.
        """
        try:
            # Prepare messages
            messages = []
            
            # Add system message if provided
            if system_message:
                messages.append({"role": "system", "content": system_message})
            
            # Add user prompt
            messages.append({"role": "user", "content": prompt})
            
            # Make streaming API call
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                stream=True
            )
            
            # Yield response chunks as they arrive
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            raise Exception(f"Error calling ChatGPT API: {str(e)}")


# Convenience function for quick usage
def quick_chat(prompt: str, api_key: Optional[str] = None, model: str = "gpt-4o") -> str:
    """
    Quick function to send a prompt to ChatGPT without creating a class instance.
    
    Args:
        prompt (str): The prompt to send to ChatGPT.
        api_key (str, optional): OpenAI API key.
        model (str): The model to use. Defaults to "gpt-4o".
    
    Returns:
        str: The response from ChatGPT.
    """
    api = ChatGPTAPI(api_key=api_key, model=model)
    return api.chat(prompt)

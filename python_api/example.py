"""
Example usage of the ChatGPT API wrapper.

This script demonstrates how to use the ChatGPTAPI class to interact with
OpenAI's ChatGPT API.
"""

from chatgpt_api import ChatGPTAPI, quick_chat


def main():
    """
    Demonstrate various ways to use the ChatGPT API wrapper.
    """
    print("ChatGPT API Example Usage")
    print("=" * 40)
    
    try:
        # Method 1: Using the ChatGPTAPI class
        print("\n1. Using ChatGPTAPI class:")
        api = ChatGPTAPI()
        
        # Simple chat
        prompt = "What is Python programming language?"
        response = api.chat(prompt)
        print(f"Prompt: {prompt}")
        print(f"Response: {response}")
        
        # Chat with system message
        print("\n2. Chat with system message:")
        system_msg = "You are a helpful coding assistant that explains things simply."
        prompt = "How do I create a list in Python?"
        response = api.chat(prompt, system_message=system_msg)
        print(f"System: {system_msg}")
        print(f"Prompt: {prompt}")
        print(f"Response: {response}")
        
        # Method 2: Using the quick_chat function
        print("\n3. Using quick_chat function:")
        prompt = "Tell me a fun fact about space."
        response = quick_chat(prompt)
        print(f"Prompt: {prompt}")
        print(f"Response: {response}")
        
        # Method 3: Streaming response
        print("\n4. Streaming response:")
        prompt = "Write a short poem about coding."
        print(f"Prompt: {prompt}")
        print("Response (streaming): ", end="", flush=True)
        
        for chunk in api.chat_stream(prompt):
            print(chunk, end="", flush=True)
        print()  # New line after streaming
        
    except Exception as e:
        print(f"Error: {e}")
        print("\nMake sure you have:")
        print("1. Set your OPENAI_API_KEY environment variable")
        print("2. Installed the required dependencies: pip install -r requirements.txt")


if __name__ == "__main__":
    main()

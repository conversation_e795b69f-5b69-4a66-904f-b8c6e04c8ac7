#!/usr/bin/env python3
"""
Interactive test script for the ChatGPT API wrapper.

This script allows you to test the API interactively by entering your API key
and trying different prompts and token limits.
"""

import os
import sys
from chatgpt_api import ChatGPTAPI, quick_chat


def test_with_api_key(api_key):
    """Test the API with a provided API key."""
    print("\n" + "="*50)
    print("Testing ChatGPT API Wrapper")
    print("="*50)
    
    try:
        # Test 1: Basic initialization
        print("\n1. Testing basic initialization...")
        api = ChatGPTAPI(api_key=api_key)
        print(f"   ✓ API initialized successfully")
        print(f"   ✓ Model: {api.model}")
        print(f"   ✓ Default max tokens: {api.max_tokens}")
        
        # Test 2: Simple chat with default settings
        print("\n2. Testing simple chat...")
        prompt = "Say 'Hello, this is a test!' and nothing else."
        print(f"   Prompt: {prompt}")
        response = api.chat(prompt)
        print(f"   Response: {response}")
        
        # Test 3: Token limit control
        print("\n3. Testing token limit control...")
        prompt = "Explain what Python is in detail."
        print(f"   Prompt: {prompt}")
        print("   Testing with 50 token limit...")
        limited_response = api.chat(prompt, max_tokens=50)
        print(f"   Limited response: {limited_response}")
        
        # Test 4: Quick chat function
        print("\n4. Testing quick_chat function...")
        prompt = "Tell me a fun fact."
        print(f"   Prompt: {prompt}")
        quick_response = quick_chat(prompt, api_key=api_key, max_tokens=100)
        print(f"   Quick response: {quick_response}")
        
        # Test 5: Streaming (optional)
        print("\n5. Testing streaming response...")
        prompt = "Count from 1 to 5."
        print(f"   Prompt: {prompt}")
        print("   Streaming response: ", end="", flush=True)
        try:
            for chunk in api.chat_stream(prompt, max_tokens=50):
                print(chunk, end="", flush=True)
            print()  # New line
        except Exception as e:
            print(f"\n   Streaming error: {e}")
        
        print("\n" + "="*50)
        print("✓ All tests completed successfully!")
        print("✓ Your API wrapper is working correctly.")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        return False
    
    return True


def main():
    """Main interactive testing function."""
    print("ChatGPT API Wrapper - Interactive Test")
    print("="*40)
    
    # Check if API key is already set
    existing_key = os.getenv("OPENAI_API_KEY")
    if existing_key and existing_key != "your_openai_api_key_here":
        print(f"Found existing API key: {existing_key[:8]}...")
        use_existing = input("Use existing API key? (y/n): ").lower().strip()
        if use_existing in ['y', 'yes']:
            return test_with_api_key(existing_key)
    
    # Get API key from user
    print("\nTo test the API, you need an OpenAI API key.")
    print("You can get one from: https://platform.openai.com/api-keys")
    print("\nOptions:")
    print("1. Enter your API key now (temporary)")
    print("2. Set OPENAI_API_KEY environment variable")
    print("3. Edit the .env file")
    print("4. Exit")
    
    choice = input("\nChoose an option (1-4): ").strip()
    
    if choice == "1":
        api_key = input("\nEnter your OpenAI API key: ").strip()
        if not api_key:
            print("No API key provided. Exiting.")
            return False
        return test_with_api_key(api_key)
    
    elif choice == "2":
        print("\nSet your API key like this:")
        print("export OPENAI_API_KEY='your_api_key_here'")
        print("Then run this script again.")
        return False
    
    elif choice == "3":
        print("\nEdit the .env file and replace 'your_openai_api_key_here' with your actual API key.")
        print("Then run this script again.")
        return False
    
    elif choice == "4":
        print("Exiting.")
        return False
    
    else:
        print("Invalid choice. Exiting.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

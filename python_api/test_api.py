"""
Simple test script for the ChatGPT API wrapper.

This script tests the basic functionality without making actual API calls
(unless you have a valid API key set up).
"""

import os
from chatgpt_api import ChatGPTAPI, quick_chat


def test_initialization():
    """Test API initialization."""
    print("Testing API initialization...")
    
    try:
        # Test with no API key (should fail gracefully)
        if not os.getenv("OPENAI_API_KEY"):
            print("  ✗ No API key found (expected)")
            try:
                api = ChatGPTAPI()
                print("  ✗ Should have failed without API key")
            except ValueError as e:
                print(f"  ✓ Correctly failed with: {e}")
        else:
            # Test with API key
            api = ChatGPTAPI()
            print("  ✓ API initialized successfully")
            print(f"  ✓ Using model: {api.model}")
    
    except Exception as e:
        print(f"  ✗ Unexpected error: {e}")


def test_with_mock_key():
    """Test API with a mock key (won't make real calls)."""
    print("\nTesting with mock API key...")
    
    try:
        # Initialize with a fake key
        api = ChatGPTAPI(api_key="fake-key-for-testing")
        print("  ✓ API initialized with mock key")
        print(f"  ✓ Model set to: {api.model}")
        
        # Note: We won't actually call the API here since it would fail
        print("  ✓ Ready for API calls (not testing actual calls with fake key)")
        
    except Exception as e:
        print(f"  ✗ Error: {e}")


def test_real_api_call():
    """Test real API call if valid key is available."""
    print("\nTesting real API call...")
    
    if not os.getenv("OPENAI_API_KEY"):
        print("  ⚠ Skipping real API test - no OPENAI_API_KEY found")
        print("  ⚠ Set OPENAI_API_KEY environment variable to test real calls")
        return
    
    try:
        api = ChatGPTAPI()
        
        # Simple test prompt
        prompt = "Say 'Hello, this is a test!' and nothing else."
        print(f"  → Sending prompt: {prompt}")
        
        response = api.chat(prompt)
        print(f"  ✓ Received response: {response}")
        
        # Test quick_chat function
        quick_response = quick_chat("Say 'Quick test successful!'")
        print(f"  ✓ Quick chat response: {quick_response}")
        
    except Exception as e:
        print(f"  ✗ API call failed: {e}")


def main():
    """Run all tests."""
    print("ChatGPT API Wrapper Tests")
    print("=" * 40)
    
    test_initialization()
    test_with_mock_key()
    test_real_api_call()
    
    print("\n" + "=" * 40)
    print("Test Summary:")
    print("- Basic initialization: Tested")
    print("- Mock key handling: Tested")
    print("- Real API calls: Conditional on API key")
    print("\nTo test real API calls, set your OPENAI_API_KEY environment variable.")


if __name__ == "__main__":
    main()
